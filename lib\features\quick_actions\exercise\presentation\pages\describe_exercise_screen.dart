import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/common/extentions/size_extension.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/core/di/injection.dart';
import 'package:cal/features/quick_actions/exercise/data/models/exercise_save_ai_model.dart';
import 'package:cal/features/quick_actions/exercise/presentation/bloc/exercise_bloc.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DescribeExerciseScreen extends StatefulWidget {
  const DescribeExerciseScreen({super.key});

  @override
  State<DescribeExerciseScreen> createState() => _DescribeExerciseScreenState();
}

class _DescribeExerciseScreenState extends State<DescribeExerciseScreen> {
  final TextEditingController _descriptionController = TextEditingController();

  @override
  void dispose() {
    super.dispose();

    _descriptionController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ExerciseBloc>(
      create: (context) => getIt<ExerciseBloc>(),
      child: Scaffold(
          appBar: AppBar(
            toolbarHeight: context.screenHeight * 0.1,
            title: AppText.titleLarge(
              LocaleKeys.exercise_describe_exercise_title.tr(),
              color: context.onSecondary,
            ),
            centerTitle: true,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () {
                context.pop();
              },
            ),
          ),
          body: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextField(
                  controller: _descriptionController,
                  maxLines: 1,
                  decoration: InputDecoration(
                    hintText: 'صف وقت التمرين، وشدته، وما إلى ذلك.',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: context.primaryColor),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: context.primaryColor, width: 1),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                Container(
                  width: double.maxFinite,
                  padding: const EdgeInsets.all(16.0),
                  decoration: BoxDecoration(
                    color: context.onTertiary.withAlpha(150),
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  child: AppText.bodyMedium(
                    'مثال: يوغا ل 60 دقيقة، مع التمدد و الاسترخاء',
                    color: context.onSecondary,
                  ),
                ),
                const Spacer(),
                SizedBox(
                  width: double.infinity,
                  child: BlocBuilder<ExerciseBloc, ExerciseState>(
                    builder: (context, state) {
                      final isLoading = state is ExerciseLoading;
                      return ElevatedButton(
                        onPressed: isLoading
                            ? null
                            : () {
                                final exercise = ExerciseSaveAiModel(
                                  description: _descriptionController.text,
                                );
                                context.read<ExerciseBloc>().add(SaveExerciseAi(exercise: exercise));
                                // Immediately navigate back to home screen
                                Navigator.of(context).pop();
                              },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: context.primaryColor,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: isLoading
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )
                            : AppText.titleLarge(
                                'اضافة التمرين',
                                color: context.onPrimaryColor,
                                fontWeight: FontWeight.bold,
                              ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
