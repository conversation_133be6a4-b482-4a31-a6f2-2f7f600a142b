part of 'scan_food_bloc.dart';

abstract class ScanFoodEvent extends Equatable {
  const ScanFoodEvent();

  @override
  List<Object?> get props => [];
}

// Food Recognition Events
class ProcessCapturedImageEvent extends ScanFoodEvent {
  final File imageFile;
  final bool isBarcode;
  final bool isLabel;
  final String? barcode;

  const ProcessCapturedImageEvent({required this.imageFile, this.isBarcode = false, this.barcode, required this.isLabel});

  @override
  List<Object?> get props => [imageFile];
}

class PickImageFromGalleryEvent extends ScanFoodEvent {
  final BuildContext? context;
  const PickImageFromGalleryEvent({this.context});
}

class RecognizeFoodEvent extends ScanFoodEvent {
  final File image;
  final bool isLabel;

  const RecognizeFoodEvent({required this.image, required this.isLabel});

  @override
  List<Object?> get props => [image];
}

class RetryRecognizeFoodEvent extends ScanFoodEvent {
  const RetryRecognizeFoodEvent();

  @override
  List<Object?> get props => [];
}

class ScanBarcodeEvent extends ScanFoodEvent{
  final String barcode;
  final File imageFile;

  const ScanBarcodeEvent({required this.barcode, required this.imageFile});
}