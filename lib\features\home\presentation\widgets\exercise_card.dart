import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:cal/core/local_models/exercise_model/exercise_model.dart';
import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/generated/locale_keys.g.dart';

class ExerciseCard extends StatelessWidget {
  const ExerciseCard({
    super.key,
    required this.exerciseModel,
    required this.onDelete,
    this.onRetry,
  });

  final ExerciseModel exerciseModel;
  final VoidCallback onDelete;
  final VoidCallback? onRetry;

  @override
  Widget build(BuildContext context) {
    return Slidable(
      key: ValueKey("${exerciseModel.id}_${exerciseModel.date}_${exerciseModel.isLoading}_${exerciseModel.hasError}"),
      endActionPane: _buildActionPane(),
      child: _buildCardContainer(context),
    );
  }

  ActionPane _buildActionPane() {
    return ActionPane(
      motion: const DrawerMotion(),
      extentRatio: 0.25,
      children: [
        SlidableAction(
          onPressed: (_) => onDelete(),
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
          icon: Icons.delete,
          label: LocaleKeys.home_delete.tr(),
          borderRadius: BorderRadius.circular(20),
        ),
      ],
    );
  }

  Widget _buildCardContainer(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: const Color(0xfffefefe),
        boxShadow: [
          BoxShadow(
            color: context.onSecondary.withAlpha(35),
            offset: const Offset(-2, 3),
            blurRadius: 10,
          ),
        ],
      ),
      child: Row(
        children: [
          _buildExerciseIcon(context),
          const SizedBox(width: 16),
          Expanded(
            child: Padding(
              padding: const EdgeInsetsDirectional.symmetric(horizontal: 10, vertical: 16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHeaderRow(context),
                  const SizedBox(height: 8),
                  _buildDetailsRow(context),
                  const SizedBox(height: 4),
                  _buildTimeAndIntensityRow(context),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExerciseIcon(BuildContext context) {
    IconData iconData;
    Color iconColor;

    // Show error or loading state icons
    if (exerciseModel.hasError) {
      iconData = Icons.error_outline;
      iconColor = Colors.red;
    } else if (exerciseModel.isLoading) {
      iconData = Icons.fitness_center; // Default icon while loading
      iconColor = Colors.grey;
    } else {
      // Determine icon based on exercise type
      final exerciseType = exerciseModel.typeEnglish?.toLowerCase() ?? '';
      switch (exerciseType) {
        case 'run':
        case 'running':
          iconData = Icons.directions_run;
          iconColor = Colors.orange;
          break;
        case 'weight_lifting':
        case 'weightlifting':
          iconData = Icons.fitness_center;
          iconColor = Colors.blue;
          break;
        case 'cycling':
          iconData = Icons.directions_bike;
          iconColor = Colors.green;
          break;
        case 'swimming':
          iconData = Icons.pool;
          iconColor = Colors.cyan;
          break;
        case 'yoga':
          iconData = Icons.self_improvement;
          iconColor = Colors.purple;
          break;
        default:
          iconData = Icons.sports_gymnastics;
          iconColor = Colors.grey;
      }
    }

    return Container(
      width: 80,
      height: 80,
      margin: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: iconColor.withAlpha(25),
      ),
      child: exerciseModel.isLoading
          ? const CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
            )
          : exerciseModel.hasError && onRetry != null
              ? GestureDetector(
                  onTap: onRetry,
                  child: const Icon(
                    Icons.refresh,
                    color: Colors.red,
                    size: 40,
                  ),
                )
              : Icon(
                  iconData,
                  size: 40,
                  color: iconColor,
                ),
    );
  }

  Widget _buildHeaderRow(BuildContext context) {
    final exerciseName = exerciseModel.typeArabic ?? exerciseModel.typeEnglish ?? 'تمرين';

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Flexible(
          child: AppText.titleMedium(
            exerciseName,
            color: context.onSecondary,
            fontWeight: FontWeight.bold,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        if (exerciseModel.calories != null)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: context.primaryColor.withAlpha(25),
            ),
            child: AppText.bodySmall(
              '${exerciseModel.calories} سعرة',
              color: context.primaryColor.withAlpha(130),
              fontWeight: FontWeight.w600,
            ),
          ),
      ],
    );
  }

  Widget _buildDetailsRow(BuildContext context) {
    return Row(
      children: [
        if (exerciseModel.duration != null) ...[
          Icon(
            Icons.access_time,
            size: 16,
            color: context.onSecondary.withAlpha(130),
          ),
          const SizedBox(width: 4),
          AppText.bodyMedium(
            '${exerciseModel.duration} دقيقة',
            color: context.onSecondary.withAlpha(220),
          ),
        ],
      ],
    );
  }

  Widget _buildTimeAndIntensityRow(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        if (exerciseModel.date != null)
          AppText.bodySmall(
            DateFormat('HH:mm').format(exerciseModel.date!),
            color: context.onSecondary.withAlpha(130),
          ),
        if (exerciseModel.intensity != null)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: _getIntensityColor(exerciseModel.intensity!).withAlpha(25),
            ),
            child: AppText.bodySmall(
              _getIntensityText(exerciseModel.intensity!),
              color: _getIntensityColor(exerciseModel.intensity!),
              fontWeight: FontWeight.w500,
            ),
          ),
      ],
    );
  }

  Color _getIntensityColor(String intensity) {
    switch (intensity.toLowerCase()) {
      case 'high':
        return Colors.red;
      case 'mid':
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  String _getIntensityText(String intensity) {
    switch (intensity.toLowerCase()) {
      case 'high':
        return 'عالية';
      case 'mid':
      case 'medium':
        return 'متوسطة';
      case 'low':
        return 'منخفضة';
      default:
        return intensity;
    }
  }
}
