part of 'scan_food_bloc.dart';

enum ScanFoodStatus { initial, loading, processing, success, error }

class ScanFoodState extends Equatable {
  final ScanFoodStatus status;
  final File? capturedImage;
  final String? errorMessage;
  final FoodModel? recognizedFood;
  final FoodModel? scanBarcode;
  final bool isRetry;

  const ScanFoodState({
    this.status = ScanFoodStatus.initial,
    this.capturedImage,
    this.errorMessage,
    this.recognizedFood,
    this.isRetry = false,
    this.scanBarcode,
  });

  ScanFoodState copyWith({
    ScanFoodStatus? status,
    File? capturedImage,
    String? errorMessage,
    FoodModel? recognizedFood,
    bool? isRetry,
    FoodModel? scanBarcode,
  }) {
    return ScanFoodState(
      scanBarcode: scanBarcode ?? this.scanBarcode,
      status: status ?? this.status,
      capturedImage: capturedImage ?? this.capturedImage,
      errorMessage: errorMessage,
      recognizedFood: recognizedFood,
      isRetry: isRetry ?? this.isRetry,
    );
  }

  @override
  List<Object?> get props => [
        status,
        capturedImage,
        errorMessage,
        recognizedFood,
        isRetry,
        scanBarcode,
      ];
}
