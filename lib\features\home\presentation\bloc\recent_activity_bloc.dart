import 'dart:async';
import 'dart:developer';

import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/core/local_models/exercise_model/exercise_model.dart';
import 'package:cal/features/home/<USER>/repositories/food_repository.dart';
import 'package:cal/features/home/<USER>/repositories/exercise_repository.dart';
import 'package:cal/features/home/<USER>/usecases/get_daily_user_data_usecase.dart';
import 'package:cal/features/home/<USER>/usecases/update_daily_user_data_usecase.dart';
import 'package:cal/features/home/<USER>/usecases/get_daily_exercises_usecase.dart';
import 'package:cal/features/home/<USER>/usecases/delete_exercise_usecase.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';

part 'recent_activity_event.dart';
part 'recent_activity_state.dart';

@singleton
class RecentActivityBloc extends Bloc<RecentActivityEvent, RecentActivityState> {
  final FoodRepository foodRepository;
  final HomeExerciseRepository exerciseRepository;
  final GetDailyUserDataUseCase getDailyUserDataUseCase;
  final UpdateDailyUserDataUseCase updateDailyUserDataUseCase;
  final GetDailyExercisesUseCase getDailyExercisesUseCase;
  final DeleteExerciseUseCase deleteExerciseUseCase;

  RecentActivityBloc({
    required this.foodRepository,
    required this.exerciseRepository,
    required this.getDailyUserDataUseCase,
    required this.updateDailyUserDataUseCase,
    required this.getDailyExercisesUseCase,
    required this.deleteExerciseUseCase,
  }) : super(const RecentActivityState()) {
    on<LoadActivity>(_onLoadActivity);
    on<AddFood>(_onAddFood);
    on<UpdateFood>(_onUpdateFood);
    on<UpdateFoodWithDifferences>(_onUpdateFoodWithDifferences);
    on<ClearFood>(_onClearFood);
    on<DeleteFood>(_onDeleteFood);
    on<DeleteExercise>(_onDeleteExercise);
  }

  Future<void> _onLoadActivity(LoadActivity event, Emitter<RecentActivityState> emit) async {
    emit(state.copyWith(status: RecentActivityStatus.loading));
    try {
      final meals = await foodRepository.getAllFood(event.date);
      final exercises = await getDailyExercisesUseCase(event.date);

      emit(state.copyWith(
        status: RecentActivityStatus.success,
        foodList: meals,
        exerciseList: exercises,
      ));
    } catch (e) {
      emit(state.copyWith(status: RecentActivityStatus.failure, errorMessage: e.toString()));
    }
  }

  Future<void> _onAddFood(AddFood event, Emitter<RecentActivityState> emit) async {
    final currentList = [...state.foodList];
    final updatedList = [event.meal, ...currentList];
    emit(state.copyWith(foodList: List.of(updatedList)));
  }

  Future<void> _onUpdateFood(UpdateFood event, Emitter<RecentActivityState> emit) async {
    final updatedList = state.foodList.map((food) {
      // Check for tempId first, then imagePath, then id
      if (food.tempId != null && food.tempId == event.meal.tempId) {
        return event.meal;
      } else if (food.imagePath == event.meal.imagePath && food.isLoading) {
        return event.meal;
      } else if (food.id == event.meal.id) {
        return event.meal;
      }
      return food;
    }).toList();

    emit(state.copyWith(status: RecentActivityStatus.success, foodList: List.of(updatedList)));
    log("Updated: event.meal.isLoading: ${event.meal.isLoading}");

    if (!event.meal.isLoading && !event.meal.hasError) {
      await foodRepository.saveMeal(event.meal);
      await _updateDailyUserData(
        event.meal.date!,
        event.meal.calories?.toDouble() ?? 0.0,
        event.meal.carbs ?? 0.0,
        event.meal.protein ?? 0.0,
        event.meal.fat ?? 0.0,
      );
    }
  }

  Future<void> _onUpdateFoodWithDifferences(UpdateFoodWithDifferences event, Emitter<RecentActivityState> emit) async {
    // Update the UI state first
    final updatedList = state.foodList.map((food) {
      // Check for tempId first, then imagePath, then id
      if (food.tempId != null && food.tempId == event.meal.tempId) {
        return event.meal;
      } else if (food.imagePath == event.meal.imagePath && food.isLoading) {
        return event.meal;
      } else if (food.id == event.meal.id) {
        return event.meal;
      }
      return food;
    }).toList();

    emit(state.copyWith(status: RecentActivityStatus.success, foodList: List.of(updatedList)));
    log("Updated with differences: event.meal.isLoading: ${event.meal.isLoading}");

    // Save to database if not loading and no error
    if (!event.meal.isLoading && !event.meal.hasError) {
      await foodRepository.saveMeal(event.meal);

      log("Updating daily data with provided differences - Calories: ${event.caloriesDiff}, Carbs: ${event.carbsDiff}, Protein: ${event.proteinDiff}, Fat: ${event.fatDiff}");

      // Update daily data with the provided differences
      await _updateDailyUserData(
        event.meal.date!,
        event.caloriesDiff,
        event.carbsDiff,
        event.proteinDiff,
        event.fatDiff,
      );
    }
  }

  Future<void> _onClearFood(ClearFood event, Emitter<RecentActivityState> emit) async {
    emit(state.copyWith(status: RecentActivityStatus.loading));

    try {
      await foodRepository.clearFood();

      emit(state.copyWith(status: RecentActivityStatus.success, foodList: const []));
    } catch (e) {
      emit(state.copyWith(status: RecentActivityStatus.failure, errorMessage: e.toString()));
    }
  }

  Future<void> _onDeleteFood(DeleteFood event, Emitter<RecentActivityState> emit) async {
    await foodRepository.deleteFood(event.meal);

    final updatedList = state.foodList.where((food) => food != event.meal).toList();

    emit(state.copyWith(status: RecentActivityStatus.success, foodList: updatedList));
    if (!event.meal.hasError && !event.meal.isLoading) {
      await _decrementDailyUserData(
          event.meal.date!, event.meal.calories!.toDouble(), event.meal.carbs!, event.meal.protein!, event.meal.fat!);
    }
  }

  Future<void> _onDeleteExercise(DeleteExercise event, Emitter<RecentActivityState> emit) async {
    await deleteExerciseUseCase(event.exercise);

    final updatedList = state.exerciseList.where((exercise) => exercise != event.exercise).toList();

    emit(state.copyWith(status: RecentActivityStatus.success, exerciseList: updatedList));
  }

  Future<void> _updateDailyUserData(DateTime date, double calories, double carbs, double protein, double fat) async {
    final currentDailyData = await getDailyUserDataUseCase(date);
    final updatedDailyData = currentDailyData.copyWith(
      consumedCalories: currentDailyData.consumedCalories + calories,
      consumedCarbs: currentDailyData.consumedCarbs + carbs,
      consumedProtein: currentDailyData.consumedProtein + protein,
      consumedFat: currentDailyData.consumedFat + fat,
    );
    await updateDailyUserDataUseCase(updatedDailyData);
  }

  Future<void> _decrementDailyUserData(DateTime date, double calories, double carbs, double protein, double fat) async {
    final currentDailyData = await getDailyUserDataUseCase(date);
    final updatedDailyData = currentDailyData.copyWith(
      consumedCalories: (currentDailyData.consumedCalories - calories).clamp(0.0, double.infinity),
      consumedCarbs: (currentDailyData.consumedCarbs - carbs).clamp(0.0, double.infinity),
      consumedProtein: (currentDailyData.consumedProtein - protein).clamp(0.0, double.infinity),
      consumedFat: (currentDailyData.consumedFat - fat).clamp(0.0, double.infinity),
    );
    await updateDailyUserDataUseCase(updatedDailyData);
  }


}
