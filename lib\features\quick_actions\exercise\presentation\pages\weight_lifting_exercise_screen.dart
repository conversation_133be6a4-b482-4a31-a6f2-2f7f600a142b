import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/size_extension.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/core/di/injection.dart';
import 'package:cal/features/home/<USER>/bloc/recent_activity_bloc.dart';
import 'package:cal/features/quick_actions/exercise/data/models/exercise_save_model.dart';
import 'package:cal/features/quick_actions/exercise/domain/services/calorie_calculation_service.dart';
import 'package:cal/features/quick_actions/exercise/domain/usecases/save_exercise_usecase.dart';
import 'package:cal/features/quick_actions/exercise/domain/usecases/save_exercise_ai_usecase.dart';
import 'package:cal/features/quick_actions/exercise/presentation/bloc/exercise_bloc.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class WeightLiftingExerciseScreen extends StatefulWidget {
  const WeightLiftingExerciseScreen({super.key});

  @override
  State<WeightLiftingExerciseScreen> createState() => _WeightLiftingExerciseScreenState();
}

class _WeightLiftingExerciseScreenState extends State<WeightLiftingExerciseScreen> {
  double _currentSliderValue = 15;
  double _sliderValue = 1; // 0: Low, 1: Medium, 2: High
  late TextEditingController _durationController;

  @override
  void initState() {
    super.initState();
    _durationController = TextEditingController(text: _currentSliderValue.round().toString());
  }

  @override
  void dispose() {
    _durationController.dispose();
    super.dispose();
  }

  String get selectedIntensity {
    switch (_sliderValue.round()) {
      case 0:
        return 'low';
      case 1:
        return 'mid';
      case 2:
      default:
        return 'high';
    }
  }

  void _updateDuration(double value) {
    setState(() {
      _currentSliderValue = value;
      _durationController.text = value.round().toString();
    });
  }

  Future<void> _handleSaveExercise() async {
    try {
      // Calculate calories using the calorie calculation service
      final calorieService = getIt<CalorieCalculationService>();
      final result = await calorieService.calculateCalories(
        exerciseType: 'weight_lifting',
        intensity: selectedIntensity,
        durationMinutes: _currentSliderValue.round(),
      );

      final exercise = ExerciseSaveModel(
        calories: result.calories,
        intensity: selectedIntensity,
        duration: _currentSliderValue.round(),
        typeEnglish: 'weight_lifting',
        typeArabic: 'رفع أوزان',
      );

      if (mounted) {
        context.read<ExerciseBloc>().add(SaveExercise(exercise: exercise));
        // Immediately navigate back to home screen
        Navigator.of(context).popUntil((route) => route.isFirst);
      }
    } catch (e) {
      // Handle error - show snackbar or dialog
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error calculating calories: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final width = context.screenWidth;

    return BlocProvider<ExerciseBloc>(
      create: (context) => ExerciseBloc(
        saveExerciseUseCase: getIt<SaveExerciseUseCase>(),
        saveExerciseAiUseCase: getIt<SaveExerciseAiUseCase>(),
        recentActivityBloc: getIt<RecentActivityBloc>(),
      ),
      child: Scaffold(
        appBar: AppBar(
          toolbarHeight: context.screenHeight * 0.1,
          title: AppText.titleLarge(
            LocaleKeys.exercise_run_title.tr(),
            color: context.onSecondary,
          ),
          centerTitle: true,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: Column(
          children: [
            // Scrollable content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Intensity Section
                    AppText.titleLarge(
                      'ضبط الشدة',
                      color: context.onSecondary,
                      fontWeight: FontWeight.bold,
                    ),
                    const SizedBox(height: 20),
                    Container(
                      decoration: BoxDecoration(
                        color: context.onTertiary.withAlpha(150),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: IntrinsicHeight(
                        child: Row(
                          children: [
                            Expanded(
                              child: Padding(
                                padding: EdgeInsets.symmetric(
                                  horizontal: width * 0.07,
                                  vertical: width * 0.05,
                                ),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    _buildIntensityLabel(
                                      title: 'عالي',
                                      subtitle: 'الركض السريع - 14 ميلا بالساعة (4 دقائق لكل ميل)',
                                      intensityLevel: 2,
                                      isSelected: _sliderValue.round() == 2,
                                      context: context,
                                    ),
                                    const SizedBox(height: 30),
                                    _buildIntensityLabel(
                                      title: 'متوسط',
                                      subtitle: 'الركض - 6 اميال بالساعة (10 دقائق لكل ميل)',
                                      intensityLevel: 1,
                                      isSelected: _sliderValue.round() == 1,
                                      context: context,
                                    ),
                                    const SizedBox(height: 30),
                                    _buildIntensityLabel(
                                      title: 'منخفض',
                                      subtitle: 'المشي الهادئ - 3 اميال بالساعة (20 دقيقة لكل ميل)',
                                      intensityLevel: 0,
                                      isSelected: _sliderValue.round() == 0,
                                      context: context,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            Container(
                              width: 50,
                              padding: const EdgeInsets.symmetric(vertical: 20),
                              child: RotatedBox(
                                quarterTurns: 1,
                                child: SliderTheme(
                                  data: const SliderThemeData(trackHeight: 6),
                                  child: Slider(
                                    value: _sliderValue,
                                    min: 0,
                                    max: 2,
                                    divisions: 2,
                                    activeColor: context.primaryColor,
                                    onChanged: (value) {
                                      setState(() {
                                        _sliderValue = value;
                                      });
                                    },
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 5),
                          ],
                        ),
                      ),
                    ),

                    // Duration Section
                    const SizedBox(height: 30),
                    AppText.titleLarge(
                      'المدة',
                      color: context.onSecondary,
                      fontWeight: FontWeight.bold,
                    ),
                    const SizedBox(height: 20),

                    // Duration Buttons
                    SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        spacing: 5,
                        children: [
                          _buildDurationButton(context, 15),
                          _buildDurationButton(context, 30),
                          _buildDurationButton(context, 60),
                          _buildDurationButton(context, 90),
                        ],
                      ),
                    ),

                    // Custom Duration Input
                    const SizedBox(height: 20),
                    TextField(
                      controller: _durationController,
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        labelText: 'المدة بالدقائق',
                        hintText: 'أدخل المدة',
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      onChanged: (value) {
                        final parsedValue = int.tryParse(value);
                        if (parsedValue != null && parsedValue <= 120 && parsedValue >= 1) {
                          setState(() {
                            _currentSliderValue = parsedValue.toDouble();
                          });
                        }
                      },
                    ),

                    // Add some bottom padding to prevent content from being hidden behind the button
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),

            // Fixed Bottom Button
            Container(
              padding: const EdgeInsets.all(24.0),
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(25),
                    blurRadius: 10,
                    offset: const Offset(0, -5),
                  ),
                ],
              ),
              child: SizedBox(
                width: double.infinity,
                child: BlocBuilder<ExerciseBloc, ExerciseState>(
                  builder: (context, state) {
                    final isLoading = state is ExerciseLoading;
                    return ElevatedButton(
                      onPressed: isLoading
                          ? null
                          : () {
                              _handleSaveExercise();
                            },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: context.primaryColor,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : AppText.titleLarge(
                              'التالي',
                              color: context.onPrimaryColor,
                              fontWeight: FontWeight.bold,
                            ),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDurationButton(BuildContext context, int duration) {
    final isSelected = _currentSliderValue == duration;
    return ElevatedButton(
      onPressed: () => _updateDuration(duration.toDouble()),
      style: ButtonStyle(
        enableFeedback: true,
        side: WidgetStatePropertyAll(BorderSide(color: context.primaryColor)),
        backgroundColor: WidgetStatePropertyAll(isSelected ? context.primaryColor : Colors.white),
        shape: WidgetStatePropertyAll(RoundedRectangleBorder(borderRadius: BorderRadius.circular(24))),
      ),
      child: Text(
        '$duration دقيقة',
        style: context.textTheme.bodyMedium!.copyWith(
          color: isSelected ? context.onPrimaryColor : context.primaryColor,
        ),
      ),
    );
  }

  Widget _buildIntensityLabel({
    required String title,
    required String subtitle,
    required int intensityLevel,
    required bool isSelected,
    required BuildContext context,
  }) {
    return AnimatedScale(
      scale: isSelected ? 1.05 : 1.0,
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeInOut,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: context.textTheme.titleMedium!.copyWith(
              fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
              color: context.onSecondary,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: context.textTheme.bodyMedium!.copyWith(
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
              color: isSelected ? context.onSecondary : context.onSecondary.withAlpha(178),
            ),
          ),
        ],
      ),
    );
  }
}
