import 'package:cal/core/local_models/streak_model/streak_model.dart';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:isar/isar.dart';

import '../../common/utils/date_helper.dart';
import '../../features/home/<USER>/widgets/home_appbar.dart';

@injectable
class StreakLocalDataSource {
  final Isar _isar;

  StreakLocalDataSource(this._isar);

  Future<void> saveStreak(StreakModel streak) async {
    final dateOnly = DateTime(
      streak.streakDate.year,
      streak.streakDate.month,
      streak.streakDate.day,
    );
    final nextDay = dateOnly.add(const Duration(days: 1));

    final existing = await _isar.streakModels.filter().streakDateBetween(dateOnly, nextDay).findFirst();

    if (existing != null) {
      streak.id = existing.id;
    }

    await _isar.writeTxn(() async {
      await _isar.streakModels.put(streak);
    });
  }

  Future<List<StreakModel>> getStreak(Locale locale) async {
    List<List<Map<String, String>>> allWeeks = DateHelper.getPastFourWeeks(locale);

    List<DateTime> allDates = allWeeks.expand((week) => week.map((day) => DateTime.parse(day['fullDate']!))).toList();

    List<StreakModel> existingStreaks = await _isar.streakModels.where().findAll();

    Map<DateTime, StreakModel> existingMap = {for (var streak in existingStreaks) DateTime(streak.streakDate.year, streak.streakDate.month, streak.streakDate.day): streak};

    print('=>>>>>>>>>>>>>>> e S : ${existingStreaks}');

    List<StreakModel> finalStreaks = List.generate(allDates.length, (index) {
      return existingMap[allDates[index]] ?? StreakModel(streakDate: allDates[index], hasAction: false);
    });

    print('=>>>>>>>>>>>>>>> f S : ${finalStreaks}');

    return finalStreaks;
  }

  Future<int> getStreaksNumber() async {
    final res = await _isar.streakModels.where().findAll();
    return longestConsecutiveDays(res);
  }
}
