import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/features/quick_actions/food_database/data/models/database_food_model.dart';
import 'package:cal/features/quick_actions/food_database/data/models/remote_food_database_model.dart';
import 'package:cal/features/quick_actions/food_database/domain/repositories/food_database_repository.dart';
import 'package:cal/features/quick_actions/food_database/domain/usecases/create_meal_use_case.dart';
import 'package:cal/features/quick_actions/food_database/domain/usecases/post_meal_to_log.dart';
import 'package:cal/features/quick_actions/food_database/domain/usecases/search_meals_use_case.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';

import '../../data/models/common_remote_food_model.dart';

part 'food_database_event.dart';

part 'food_database_state.dart';

@injectable
class FoodDatabaseBloc extends Bloc<FoodDatabaseEvent, FoodDatabaseState> {
  final FoodDatabaseRepository foodDatabaseRepository;
  final SearchMealsUseCase searchMealsUseCase;
  final PostMealToLog postMealToLog;
  final CreateMealUseCase createMealUseCase;

  FoodDatabaseBloc({
    required this.foodDatabaseRepository,
    required this.searchMealsUseCase,
    required this.postMealToLog,
    required this.createMealUseCase,
  }) : super(FoodDatabaseState()) {
    on<AddFoodEvent>(_onAddFood);
    on<AddFoodToLogEvent>(_onAddFoodToLogEvent);
    on<LoadRecentFoodEvent>(_onLoadRecentFood);
    on<LoadDatabaseFoodEvent>(_onLoadDatabaseFood);
    on<LoadMyMealsEvent>(_onLoadMyMealsEvent);
    on<LoadFavoriteFoodEvent>(_onLoadFavoriteFoodEvent);
    on<AddSelectedFoodEvent>(_onAddSelectedFoodEvent);
    on<SearchFoodEvent>(_searchFood);
    on<AddIngredientToMyMealEvent>(_addIngredientToMyMeal);
    on<CreateMealEvent>(_createMeal);
  }

  FutureOr<void> _searchFood(SearchFoodEvent event, Emitter<FoodDatabaseState> emit) async {
    emit(state.copyWith(searchedFoodStatus: BlocStatus.loading));
    final res = await searchMealsUseCase(event.params);
    res.fold((l) {
      emit(state.copyWith(searchedFoodStatus: BlocStatus.error, errorMessage: l.message));
    }, (r) {
      emit(state.copyWith(searchedFoodStatus: BlocStatus.success, searchedFood: r));
    });
  }

  FutureOr<void> _createMeal(CreateMealEvent event, Emitter<FoodDatabaseState> emit) async {
    emit(state.copyWith(searchedFoodStatus: BlocStatus.loading));
    final res = await createMealUseCase(event.params);
    res.fold((l) {
      emit(state.copyWith(errorMessage: l.message));
    }, (r) {
      emit(state.copyWith(createMeal: r));
    });
  }

  Future<void> _onAddFoodToLogEvent(AddFoodToLogEvent event, Emitter<FoodDatabaseState> emit) async {
    emit(state.copyWith(status: FoodDatabaseStatus.loading));
    try {
      await foodDatabaseRepository.saveMealToLog(event.meal);

      emit(state.copyWith(status: FoodDatabaseStatus.success));

      final res = await postMealToLog(PostMealToLogParams(foodId: event.isMeal ? event.meal.id : event.meal.remoteLogId!, isMeal: event.isMeal));
      print(res);
    } catch (e) {
      print(e.toString());
      emit(state.copyWith(status: FoodDatabaseStatus.failure, errorMessage: e.toString()));
    }
  }

  Future<void> _addIngredientToMyMeal(AddIngredientToMyMealEvent event, Emitter<FoodDatabaseState> emit) async {
    emit(state.copyWith(status: FoodDatabaseStatus.loading));
    try {
      state.selectedFoods = [...state.selectedFoods, event.ingredient];

      emit(state.copyWith(status: FoodDatabaseStatus.success, selectedFoods: state.selectedFoods));
    } catch (e) {
      emit(state.copyWith(status: FoodDatabaseStatus.failure, errorMessage: e.toString()));
    }
    debugPrint('=============> selectedFoodList is ${state.selectedFoods}');
  }

  Future<void> _onAddSelectedFoodEvent(AddSelectedFoodEvent event, Emitter<FoodDatabaseState> emit) async {
    emit(state.copyWith(status: FoodDatabaseStatus.loading));
    try {
      final updatedList = List<DatabaseFoodModel>.from(state.selectedFoods)..add(event.food);
      emit(state.copyWith(status: FoodDatabaseStatus.success, selectedFoods: updatedList));
    } catch (e) {
      emit(state.copyWith(status: FoodDatabaseStatus.failure, errorMessage: e.toString()));
    }
  }

  Future<void> _onAddFood(AddFoodEvent event, Emitter<FoodDatabaseState> emit) async {
    emit(state.copyWith(status: FoodDatabaseStatus.loading));
    try {
      await foodDatabaseRepository.saveMeal(event.meal);

      if (event.isForCreateMeal) {
        add(
          CreateMealEvent(
            params: CreateMealParams(
              name: event.meal.dish!,
              ingredients: event.meal.ingredients,
              fats: event.meal.fat!,
              carbs: event.meal.carbs!,
              protein: event.meal.protein!,
              cals: event.meal.calories!,
            ),
          ),
        );
      }

      emit(state.copyWith(status: FoodDatabaseStatus.success));
    } catch (e) {
      emit(state.copyWith(status: FoodDatabaseStatus.failure, errorMessage: e.toString()));
    }
  }

  Future<void> _onLoadMyMealsEvent(LoadMyMealsEvent event, Emitter<FoodDatabaseState> emit) async {
    emit(state.copyWith(status: FoodDatabaseStatus.loading));
    try {
      final foodList = await foodDatabaseRepository.getMyMeals();

      emit(state.copyWith(status: FoodDatabaseStatus.success, myMealsList: foodList));
    } catch (e) {
      emit(state.copyWith(status: FoodDatabaseStatus.failure, errorMessage: e.toString()));
    }
  }

  Future<void> _onLoadFavoriteFoodEvent(LoadFavoriteFoodEvent event, Emitter<FoodDatabaseState> emit) async {
    emit(state.copyWith(status: FoodDatabaseStatus.loading));
    try {
      final foodList = await foodDatabaseRepository.getFavoriteFood();

      emit(state.copyWith(status: FoodDatabaseStatus.success, myFavoriteList: foodList));
    } catch (e) {
      emit(state.copyWith(status: FoodDatabaseStatus.failure, errorMessage: e.toString()));
    }
  }

  Future<void> _onLoadRecentFood(LoadRecentFoodEvent event, Emitter<FoodDatabaseState> emit) async {
    emit(state.copyWith(status: FoodDatabaseStatus.loading));
    try {
      final foodList = await foodDatabaseRepository.getRecentFood();

      emit(state.copyWith(status: FoodDatabaseStatus.success, recentFoodList: foodList));
    } catch (e) {
      emit(state.copyWith(status: FoodDatabaseStatus.failure, errorMessage: e.toString()));
    }
  }

  Future<void> _onLoadDatabaseFood(LoadDatabaseFoodEvent event, Emitter<FoodDatabaseState> emit) async {
    emit(state.copyWith(status: FoodDatabaseStatus.loading));
    try {
      final foodList = await foodDatabaseRepository.getDatabaseFood();

      emit(state.copyWith(status: FoodDatabaseStatus.success, databaseFoodList: foodList));
    } catch (e) {
      emit(state.copyWith(status: FoodDatabaseStatus.failure, errorMessage: e.toString()));
    }
  }
}
